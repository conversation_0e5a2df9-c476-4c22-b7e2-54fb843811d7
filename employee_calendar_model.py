#!/usr/bin/env python3
"""
Comprehensive Employee Calendar Model
Provides detailed overview of employee schedules, attendance, and time management
"""

from datetime import datetime, time, timedelta
import sqlite3
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

class ShiftType(Enum):
    """Enumeration of shift types"""
    MORNING = "Morning Shift"
    GENERAL = "General Shift"
    EVENING = "Evening Shift"
    NIGHT = "Night Shift"
    OVERTIME = "Overtime Shift"

class AttendanceStatus(Enum):
    """Enumeration of attendance statuses"""
    PRESENT = "Present"
    ABSENT = "Absent"
    LATE = "Late"
    LEFT_SOON = "Left Soon"
    ON_DUTY = "On-Duty"
    PERMISSION = "Permission"
    LEAVE = "Leave"

class LeaveType(Enum):
    """Enumeration of leave types"""
    SICK_LEAVE = "Sick Leave"
    CASUAL_LEAVE = "Casual Leave"
    EARNED_LEAVE = "Earned Leave"
    MATERNITY_LEAVE = "Maternity Leave"
    EMERGENCY_LEAVE = "Emergency Leave"

@dataclass
class ShiftInfo:
    """Shift information structure"""
    shift_type: str
    start_time: time
    end_time: time
    duration_hours: float
    
    def get_duration_display(self) -> str:
        """Get formatted duration display"""
        hours = int(self.duration_hours)
        minutes = int((self.duration_hours - hours) * 60)
        if minutes > 0:
            return f"{hours}h {minutes}m"
        return f"{hours} hours"

@dataclass
class PunctualityInfo:
    """Punctuality information structure"""
    late_arrival: Optional[str] = None  # "Late by HH:MM"
    early_departure: Optional[str] = None  # "Left soon by HH:MM"
    
    def has_issues(self) -> bool:
        """Check if there are any punctuality issues"""
        return self.late_arrival is not None or self.early_departure is not None

@dataclass
class AttendanceInfo:
    """Attendance status information"""
    status: str
    leave_type: Optional[str] = None
    permission_duration: Optional[str] = None
    on_duty_details: Optional[str] = None
    
    def get_status_display(self) -> str:
        """Get formatted status display"""
        if self.status == AttendanceStatus.LEAVE.value and self.leave_type:
            return f"{self.leave_type}"
        elif self.status == AttendanceStatus.PERMISSION.value and self.permission_duration:
            return f"Permission: {self.permission_duration}"
        elif self.status == AttendanceStatus.ON_DUTY.value:
            return "On-Duty"
        else:
            return self.status

@dataclass
class TimingInfo:
    """Check-in and check-out timing information"""
    check_in: Optional[str] = None
    check_out: Optional[str] = None
    
    def get_check_in_display(self) -> str:
        """Get formatted check-in display"""
        return self.check_in if self.check_in else "--:--"
    
    def get_check_out_display(self) -> str:
        """Get formatted check-out display"""
        return self.check_out if self.check_out else "--:--"

@dataclass
class EmployeeCalendarEntry:
    """Complete calendar entry for an employee on a specific day"""
    employee_id: int
    employee_name: str
    date: str
    shift_info: ShiftInfo
    punctuality_info: PunctualityInfo
    attendance_info: AttendanceInfo
    timing_info: TimingInfo
    
    def get_summary_status(self) -> str:
        """Get a summary status for quick overview"""
        if self.attendance_info.status == AttendanceStatus.ABSENT.value:
            return "❌ Absent"
        elif self.attendance_info.status == AttendanceStatus.LEAVE.value:
            return f"🏖️ {self.attendance_info.leave_type}"
        elif self.attendance_info.status == AttendanceStatus.ON_DUTY.value:
            return "🏢 On-Duty"
        elif self.attendance_info.status == AttendanceStatus.PERMISSION.value:
            return f"⏰ Permission: {self.attendance_info.permission_duration}"
        elif self.punctuality_info.has_issues():
            issues = []
            if self.punctuality_info.late_arrival:
                issues.append("Late")
            if self.punctuality_info.early_departure:
                issues.append("Left Soon")
            return f"⚠️ {' & '.join(issues)}"
        else:
            return "✅ Present"

class EmployeeCalendarModel:
    """
    Comprehensive Employee Calendar Model
    Manages all aspects of employee calendar data
    """
    
    # Shift definitions
    SHIFT_DEFINITIONS = {
        'morning': ShiftInfo('Morning Shift', time(6, 0), time(14, 0), 8.0),
        'general': ShiftInfo('General Shift', time(9, 20), time(16, 30), 7.17),
        'evening': ShiftInfo('Evening Shift', time(14, 0), time(22, 0), 8.0),
        'night': ShiftInfo('Night Shift', time(22, 0), time(6, 0), 8.0),
        'overtime': ShiftInfo('Overtime Shift', time(9, 20), time(17, 30), 8.17)
    }
    
    def __init__(self, db_path='vishnorex.db'):
        self.db_path = db_path
    
    def get_shift_info(self, shift_type: str) -> ShiftInfo:
        """Get shift information based on shift type"""
        shift_key = shift_type.lower() if shift_type else 'general'
        return self.SHIFT_DEFINITIONS.get(shift_key, self.SHIFT_DEFINITIONS['general'])
    
    def calculate_time_difference(self, actual_time: time, expected_time: time) -> Tuple[int, int]:
        """
        Calculate time difference in hours and minutes
        Returns: (hours, minutes)
        """
        actual_dt = datetime.combine(datetime.today(), actual_time)
        expected_dt = datetime.combine(datetime.today(), expected_time)

        # Handle overnight shifts
        if expected_time < actual_time and expected_time.hour < 12:
            expected_dt += timedelta(days=1)

        diff = actual_dt - expected_dt
        total_minutes = int(diff.total_seconds() / 60)

        # Take absolute value for the calculation
        total_minutes = abs(total_minutes)
        hours = total_minutes // 60
        minutes = total_minutes % 60

        return hours, minutes
    
    def format_time_difference(self, hours: int, minutes: int) -> str:
        """Format time difference for display"""
        if hours > 0 and minutes > 0:
            return f"{hours:02d}:{minutes:02d}"
        elif hours > 0:
            return f"{hours:02d}:00"
        else:
            return f"00:{minutes:02d}"
    
    def calculate_punctuality(self, shift_info: ShiftInfo, check_in: Optional[time], 
                            check_out: Optional[time]) -> PunctualityInfo:
        """Calculate punctuality information"""
        punctuality = PunctualityInfo()
        
        # Grace period for arrival (10 minutes)
        grace_period = timedelta(minutes=10)
        shift_start_with_grace = (datetime.combine(datetime.today(), shift_info.start_time) + grace_period).time()
        
        # Check late arrival
        if check_in and check_in > shift_start_with_grace:
            hours, minutes = self.calculate_time_difference(check_in, shift_start_with_grace)
            punctuality.late_arrival = f"Late by {self.format_time_difference(hours, minutes)}"
        
        # Check early departure
        if check_out and check_out < shift_info.end_time:
            hours, minutes = self.calculate_time_difference(shift_info.end_time, check_out)
            punctuality.early_departure = f"Left soon by {self.format_time_difference(hours, minutes)}"
        
        return punctuality
    
    def determine_attendance_status(self, employee_id: int, date: str, 
                                  check_in: Optional[time], check_out: Optional[time]) -> AttendanceInfo:
        """Determine attendance status based on various factors"""
        
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        try:
            # Check for leave
            cursor.execute('''
                SELECT leave_type FROM leave_applications 
                WHERE staff_id = ? AND ? BETWEEN start_date AND end_date 
                AND status = 'approved'
            ''', (employee_id, date))
            leave_record = cursor.fetchone()
            
            if leave_record:
                return AttendanceInfo(
                    status=AttendanceStatus.LEAVE.value,
                    leave_type=leave_record['leave_type']
                )
            
            # Check for on-duty
            cursor.execute('''
                SELECT permission_type, location FROM on_duty_permissions 
                WHERE staff_id = ? AND DATE(start_datetime) <= ? AND DATE(end_datetime) >= ?
                AND status = 'approved'
            ''', (employee_id, date, date))
            on_duty_record = cursor.fetchone()
            
            if on_duty_record:
                return AttendanceInfo(
                    status=AttendanceStatus.ON_DUTY.value,
                    on_duty_details=f"{on_duty_record['permission_type']} at {on_duty_record['location']}"
                )
            
            # Check for permission
            cursor.execute('''
                SELECT permission_type, duration FROM permissions 
                WHERE staff_id = ? AND permission_date = ? AND status = 'approved'
            ''', (employee_id, date))
            permission_record = cursor.fetchone()
            
            if permission_record:
                return AttendanceInfo(
                    status=AttendanceStatus.PERMISSION.value,
                    permission_duration=permission_record['duration']
                )
            
            # Determine based on check-in/check-out
            if not check_in and not check_out:
                return AttendanceInfo(status=AttendanceStatus.ABSENT.value)
            else:
                return AttendanceInfo(status=AttendanceStatus.PRESENT.value)
                
        finally:
            conn.close()
    
    def get_employee_calendar_entry(self, employee_id: int, date: str) -> Optional[EmployeeCalendarEntry]:
        """Get complete calendar entry for an employee on a specific date"""
        
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        try:
            # Get employee information
            cursor.execute('''
                SELECT id, full_name, shift_type FROM staff WHERE id = ?
            ''', (employee_id,))
            employee = cursor.fetchone()
            
            if not employee:
                return None
            
            # Get attendance record
            cursor.execute('''
                SELECT time_in, time_out FROM attendance 
                WHERE staff_id = ? AND date = ?
            ''', (employee_id, date))
            attendance = cursor.fetchone()
            
            # Parse timing information
            check_in = None
            check_out = None
            
            if attendance:
                if attendance['time_in']:
                    check_in = datetime.strptime(attendance['time_in'], '%H:%M:%S').time()
                if attendance['time_out']:
                    check_out = datetime.strptime(attendance['time_out'], '%H:%M:%S').time()
            
            # Get shift information
            shift_info = self.get_shift_info(employee['shift_type'])
            
            # Calculate punctuality
            punctuality_info = self.calculate_punctuality(shift_info, check_in, check_out)
            
            # Determine attendance status
            attendance_info = self.determine_attendance_status(employee_id, date, check_in, check_out)
            
            # Create timing info
            timing_info = TimingInfo(
                check_in=check_in.strftime('%H:%M') if check_in else None,
                check_out=check_out.strftime('%H:%M') if check_out else None
            )
            
            return EmployeeCalendarEntry(
                employee_id=employee_id,
                employee_name=employee['full_name'],
                date=date,
                shift_info=shift_info,
                punctuality_info=punctuality_info,
                attendance_info=attendance_info,
                timing_info=timing_info
            )
            
        finally:
            conn.close()
    
    def get_calendar_data_for_date(self, date: str, school_id: Optional[int] = None) -> List[EmployeeCalendarEntry]:
        """Get calendar data for all employees on a specific date"""
        
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        try:
            # Get all employees
            if school_id:
                cursor.execute('SELECT id FROM staff WHERE school_id = ?', (school_id,))
            else:
                cursor.execute('SELECT id FROM staff')
            
            employees = cursor.fetchall()
            
            calendar_entries = []
            for employee in employees:
                entry = self.get_employee_calendar_entry(employee['id'], date)
                if entry:
                    calendar_entries.append(entry)
            
            return calendar_entries
            
        finally:
            conn.close()
    
    def get_calendar_summary(self, date: str, school_id: Optional[int] = None) -> Dict:
        """Get calendar summary statistics for a date"""
        
        entries = self.get_calendar_data_for_date(date, school_id)
        
        summary = {
            'total_employees': len(entries),
            'present': 0,
            'absent': 0,
            'on_leave': 0,
            'on_duty': 0,
            'permission': 0,
            'late_arrivals': 0,
            'early_departures': 0
        }
        
        for entry in entries:
            if entry.attendance_info.status == AttendanceStatus.PRESENT.value:
                summary['present'] += 1
            elif entry.attendance_info.status == AttendanceStatus.ABSENT.value:
                summary['absent'] += 1
            elif entry.attendance_info.status == AttendanceStatus.LEAVE.value:
                summary['on_leave'] += 1
            elif entry.attendance_info.status == AttendanceStatus.ON_DUTY.value:
                summary['on_duty'] += 1
            elif entry.attendance_info.status == AttendanceStatus.PERMISSION.value:
                summary['permission'] += 1
            
            if entry.punctuality_info.late_arrival:
                summary['late_arrivals'] += 1
            if entry.punctuality_info.early_departure:
                summary['early_departures'] += 1
        
        return summary
