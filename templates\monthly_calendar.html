<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Monthly Employee Calendar - {{ school_name }}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .calendar-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem 0;
        }
        
        .monthly-calendar {
            background: white;
            border-radius: 15px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            overflow: hidden;
            margin-bottom: 2rem;
        }
        
        .calendar-nav {
            background: #f8f9fa;
            padding: 1rem;
            border-bottom: 1px solid #dee2e6;
        }
        
        .calendar-table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .calendar-day-header {
            background: #6c757d;
            color: white;
            padding: 0.75rem;
            text-align: center;
            font-weight: 600;
            font-size: 0.9rem;
            border: 1px solid #495057;
        }
        
        .calendar-day {
            height: 120px;
            padding: 0.5rem;
            vertical-align: top;
            border: 1px solid #dee2e6;
            position: relative;
        }
        
        .calendar-day.other-month {
            background: #f8f9fa;
            color: #6c757d;
        }
        
        .calendar-day.today {
            background: #e3f2fd;
            border: 2px solid #2196f3;
        }
        
        .day-number {
            font-weight: 600;
            font-size: 0.9rem;
            margin-bottom: 0.25rem;
        }
        
        .staff-entry {
            font-size: 0.7rem;
            margin-bottom: 0.15rem;
            padding: 0.1rem 0.3rem;
            border-radius: 3px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            cursor: pointer;
            display: block;
        }
        
        .staff-entry:hover {
            opacity: 0.8;
        }
        
        .status-present { background-color: #d4edda; color: #155724; }
        .status-absent { background-color: #f8d7da; color: #721c24; }
        .status-late { background-color: #fff3cd; color: #856404; }
        .status-leave { background-color: #cce7ff; color: #004085; }
        .status-permission { background-color: #e2e3e5; color: #383d41; }
        .status-onduty { background-color: #d1ecf1; color: #0c5460; }
        
        .summary-card {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .legend {
            background: white;
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .legend-item {
            display: inline-block;
            margin: 0.25rem 0.5rem;
            font-size: 0.8rem;
        }
        
        .legend-color {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 2px;
            margin-right: 0.25rem;
            vertical-align: middle;
        }
        
        .search-controls {
            min-width: 150px;
        }
        
        @media (max-width: 768px) {
            .calendar-day {
                height: 80px;
                padding: 0.25rem;
            }
            
            .staff-entry {
                font-size: 0.6rem;
            }
            
            .day-number {
                font-size: 0.8rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="calendar-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h1 class="mb-0">
                        <i class="bi bi-calendar3 me-2"></i>
                        Monthly Employee Calendar
                    </h1>
                    <p class="mb-0 opacity-75">{{ school_name }} - Monthly Attendance Overview</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <div class="d-flex align-items-center justify-content-md-end flex-wrap gap-2">
                        <div class="d-flex align-items-center">
                            <label for="monthSelector" class="form-label text-white me-2 mb-0">Month:</label>
                            <input type="month" id="monthSelector" class="form-control">
                        </div>
                        <div class="d-flex align-items-center">
                            <label for="staffSearch" class="form-label text-white me-2 mb-0">Search:</label>
                            <input type="text" id="staffSearch" class="form-control search-controls" 
                                   placeholder="Search staff...">
                        </div>
                        <button type="button" class="btn btn-light" onclick="loadCalendar()" title="Refresh">
                            <i class="bi bi-arrow-clockwise"></i>
                        </button>
                        <button type="button" class="btn btn-outline-light" onclick="clearSearch()" title="Clear">
                            <i class="bi bi-x-circle"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="container mt-4">
        <!-- Summary Section -->
        <div class="summary-card">
            <div class="row text-center">
                <div class="col-md-2">
                    <h3 id="totalEmployees">0</h3>
                    <small>Total Staff</small>
                </div>
                <div class="col-md-2">
                    <h3 id="presentCount">0</h3>
                    <small>Present</small>
                </div>
                <div class="col-md-2">
                    <h3 id="absentCount">0</h3>
                    <small>Absent</small>
                </div>
                <div class="col-md-2">
                    <h3 id="leaveCount">0</h3>
                    <small>On Leave</small>
                </div>
                <div class="col-md-2">
                    <h3 id="lateCount">0</h3>
                    <small>Late Arrivals</small>
                </div>
                <div class="col-md-2">
                    <h3 id="earlyCount">0</h3>
                    <small>Early Departures</small>
                </div>
            </div>
        </div>

        <!-- Status Legend -->
        <div class="legend">
            <h6 class="mb-2">Status Legend:</h6>
            <div class="legend-item">
                <span class="legend-color status-present"></span>Present
            </div>
            <div class="legend-item">
                <span class="legend-color status-late"></span>Late
            </div>
            <div class="legend-item">
                <span class="legend-color status-absent"></span>Absent
            </div>
            <div class="legend-item">
                <span class="legend-color status-leave"></span>Leave
            </div>
            <div class="legend-item">
                <span class="legend-color status-permission"></span>Permission
            </div>
            <div class="legend-item">
                <span class="legend-color status-onduty"></span>On-Duty
            </div>
        </div>

        <!-- Loading Spinner -->
        <div class="text-center" id="loadingSpinner" style="display: none;">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-2">Loading calendar data...</p>
        </div>

        <!-- Monthly Calendar -->
        <div class="monthly-calendar" id="monthlyCalendar">
            <div class="calendar-nav">
                <div class="d-flex justify-content-between align-items-center">
                    <button type="button" class="btn btn-outline-primary" onclick="previousMonth()">
                        <i class="bi bi-chevron-left"></i> Previous
                    </button>
                    <h4 id="currentMonthYear" class="mb-0"></h4>
                    <button type="button" class="btn btn-outline-primary" onclick="nextMonth()">
                        Next <i class="bi bi-chevron-right"></i>
                    </button>
                </div>
            </div>
            
            <table class="calendar-table">
                <thead>
                    <tr>
                        <th class="calendar-day-header">Sunday</th>
                        <th class="calendar-day-header">Monday</th>
                        <th class="calendar-day-header">Tuesday</th>
                        <th class="calendar-day-header">Wednesday</th>
                        <th class="calendar-day-header">Thursday</th>
                        <th class="calendar-day-header">Friday</th>
                        <th class="calendar-day-header">Saturday</th>
                    </tr>
                </thead>
                <tbody id="calendarBody">
                    <!-- Calendar days will be populated here -->
                </tbody>
            </table>
        </div>

        <!-- No Data Message -->
        <div id="noDataMessage" class="text-center py-5" style="display: none;">
            <i class="bi bi-calendar-x display-1 text-muted"></i>
            <h3 class="text-muted mt-3">No Data Available</h3>
            <p class="text-muted">No employee data found for the selected month.</p>
        </div>
    </div>

    <!-- Employee Detail Modal -->
    <div class="modal fade" id="employeeDetailModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="bi bi-person-circle me-2"></i>
                        <span id="modalEmployeeName">Employee Details</span>
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" id="modalEmployeeDetails">
                    <!-- Detailed employee information will be loaded here -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let currentDate = new Date();
        let calendarData = {};
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('monthSelector').value = currentDate.toISOString().slice(0, 7);
            updateMonthDisplay();
            loadCalendar();
        });
        
        // Event listeners
        document.getElementById('monthSelector').addEventListener('change', function() {
            currentDate = new Date(this.value + '-01');
            updateMonthDisplay();
            loadCalendar();
        });
        
        document.getElementById('staffSearch').addEventListener('input', function() {
            renderCalendar();
        });
        
        function updateMonthDisplay() {
            const monthNames = ["January", "February", "March", "April", "May", "June",
                "July", "August", "September", "October", "November", "December"];
            document.getElementById('currentMonthYear').textContent = 
                `${monthNames[currentDate.getMonth()]} ${currentDate.getFullYear()}`;
        }
        
        function previousMonth() {
            currentDate.setMonth(currentDate.getMonth() - 1);
            document.getElementById('monthSelector').value = currentDate.toISOString().slice(0, 7);
            updateMonthDisplay();
            loadCalendar();
        }
        
        function nextMonth() {
            currentDate.setMonth(currentDate.getMonth() + 1);
            document.getElementById('monthSelector').value = currentDate.toISOString().slice(0, 7);
            updateMonthDisplay();
            loadCalendar();
        }
        
        function loadCalendar() {
            document.getElementById('loadingSpinner').style.display = 'block';
            document.getElementById('monthlyCalendar').style.display = 'none';
            
            const year = currentDate.getFullYear();
            const month = currentDate.getMonth() + 1;
            
            fetch(`/api/calendar/month/${year}/${month}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        calendarData = data.data;
                        renderCalendar();
                        updateSummary();
                    } else {
                        showError('Failed to load calendar: ' + data.error);
                    }
                })
                .catch(error => {
                    showError('Error loading calendar: ' + error.message);
                })
                .finally(() => {
                    document.getElementById('loadingSpinner').style.display = 'none';
                    document.getElementById('monthlyCalendar').style.display = 'block';
                });
        }

        function renderCalendar() {
            const year = currentDate.getFullYear();
            const month = currentDate.getMonth();

            const firstDay = new Date(year, month, 1);
            const lastDay = new Date(year, month + 1, 0);
            const startingDayOfWeek = firstDay.getDay();

            const calendarBody = document.getElementById('calendarBody');
            calendarBody.innerHTML = '';

            let date = 1;

            // Create calendar rows
            for (let week = 0; week < 6; week++) {
                const row = document.createElement('tr');

                // Create calendar days for this week
                for (let day = 0; day < 7; day++) {
                    const cell = document.createElement('td');
                    cell.className = 'calendar-day';

                    if (week === 0 && day < startingDayOfWeek) {
                        // Previous month days
                        const prevMonth = new Date(year, month, 0);
                        const prevDate = prevMonth.getDate() - (startingDayOfWeek - day - 1);
                        cell.classList.add('other-month');
                        cell.innerHTML = `<div class="day-number">${prevDate}</div>`;
                    } else if (date > lastDay.getDate()) {
                        // Next month days
                        const nextDate = date - lastDay.getDate();
                        cell.classList.add('other-month');
                        cell.innerHTML = `<div class="day-number">${nextDate}</div>`;
                        date++;
                    } else {
                        // Current month days
                        const currentDateObj = new Date(year, month, date);
                        const today = new Date();

                        if (currentDateObj.toDateString() === today.toDateString()) {
                            cell.classList.add('today');
                        }

                        const dayHtml = `<div class="day-number">${date}</div>`;
                        const staffHtml = getStaffEntriesForDate(year, month, date);

                        cell.innerHTML = dayHtml + staffHtml;
                        date++;
                    }

                    row.appendChild(cell);
                }

                calendarBody.appendChild(row);

                // Stop if we've filled all days of the month
                if (date > lastDay.getDate()) {
                    break;
                }
            }
        }

        function getStaffEntriesForDate(year, month, day) {
            const dateStr = `${year}-${String(month + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
            const dayData = calendarData[dateStr];

            if (!dayData || !dayData.employees) {
                return '';
            }

            const searchTerm = document.getElementById('staffSearch').value.toLowerCase().trim();

            return dayData.employees
                .filter(employee => {
                    if (!searchTerm) return true;
                    return employee.employee_name.toLowerCase().includes(searchTerm) ||
                           employee.employee_id.toString().includes(searchTerm) ||
                           (employee.department && employee.department.toLowerCase().includes(searchTerm)) ||
                           (employee.position && employee.position.toLowerCase().includes(searchTerm));
                })
                .map(employee => {
                    const statusClass = getStatusClass(employee.summary_status);
                    return `<div class="staff-entry ${statusClass}"
                                 onclick="showEmployeeDetails(${employee.employee_id}, '${dateStr}')"
                                 title="${employee.employee_name} - ${employee.attendance_status}">
                                ${employee.employee_name}
                            </div>`;
                })
                .join('');
        }

        function getStatusClass(status) {
            if (status.includes('Present')) return 'status-present';
            if (status.includes('Absent')) return 'status-absent';
            if (status.includes('Late')) return 'status-late';
            if (status.includes('Leave')) return 'status-leave';
            if (status.includes('Permission')) return 'status-permission';
            if (status.includes('On-Duty')) return 'status-onduty';
            return 'status-present';
        }

        function updateSummary() {
            let totalEmployees = 0;
            let present = 0;
            let absent = 0;
            let onLeave = 0;
            let lateArrivals = 0;
            let earlyDepartures = 0;

            Object.values(calendarData).forEach(dayData => {
                if (dayData.summary) {
                    totalEmployees = Math.max(totalEmployees, dayData.summary.total_employees);
                    present += dayData.summary.present;
                    absent += dayData.summary.absent;
                    onLeave += dayData.summary.on_leave;
                    lateArrivals += dayData.summary.late_arrivals;
                    earlyDepartures += dayData.summary.early_departures;
                }
            });

            document.getElementById('totalEmployees').textContent = totalEmployees;
            document.getElementById('presentCount').textContent = present;
            document.getElementById('absentCount').textContent = absent;
            document.getElementById('leaveCount').textContent = onLeave;
            document.getElementById('lateCount').textContent = lateArrivals;
            document.getElementById('earlyCount').textContent = earlyDepartures;
        }

        function showEmployeeDetails(employeeId, date) {
            fetch(`/api/calendar/employee/${employeeId}/${date}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        displayEmployeeModal(data.data);
                    } else {
                        showError('Failed to load employee details: ' + data.error);
                    }
                })
                .catch(error => {
                    showError('Error loading employee details: ' + error.message);
                });
        }

        function displayEmployeeModal(employee) {
            document.getElementById('modalEmployeeName').textContent = employee.employee_name;

            const modalBody = document.getElementById('modalEmployeeDetails');
            modalBody.innerHTML = `
                <div class="row">
                    <div class="col-md-6">
                        <h6>Shift Information</h6>
                        <ul class="list-unstyled">
                            <li><strong>Type:</strong> ${employee.shift_info.shift_type}</li>
                            <li><strong>Duration:</strong> ${employee.shift_info.duration}</li>
                            <li><strong>Schedule:</strong> ${employee.shift_info.start_time} - ${employee.shift_info.end_time}</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>Timing Details</h6>
                        <ul class="list-unstyled">
                            <li><strong>Check-in:</strong> ${employee.timing.check_in}</li>
                            <li><strong>Check-out:</strong> ${employee.timing.check_out}</li>
                        </ul>
                    </div>
                </div>

                <div class="row mt-3">
                    <div class="col-md-6">
                        <h6>Attendance Status</h6>
                        <p>${employee.attendance.status_display}</p>
                        ${employee.attendance.leave_type ? `<p><strong>Leave Type:</strong> ${employee.attendance.leave_type}</p>` : ''}
                        ${employee.attendance.permission_duration ? `<p><strong>Permission:</strong> ${employee.attendance.permission_duration}</p>` : ''}
                        ${employee.attendance.on_duty_details ? `<p><strong>On-Duty:</strong> ${employee.attendance.on_duty_details}</p>` : ''}
                    </div>
                    <div class="col-md-6">
                        <h6>Punctuality</h6>
                        ${employee.punctuality.late_arrival ? `<p class="text-warning">${employee.punctuality.late_arrival}</p>` : ''}
                        ${employee.punctuality.early_departure ? `<p class="text-warning">${employee.punctuality.early_departure}</p>` : ''}
                        ${!employee.punctuality.has_issues ? '<p class="text-success">No punctuality issues</p>' : ''}
                    </div>
                </div>

                <div class="mt-3">
                    <h6>Summary</h6>
                    <span class="badge ${getStatusClass(employee.summary_status)}">${employee.summary_status}</span>
                </div>
            `;

            new bootstrap.Modal(document.getElementById('employeeDetailModal')).show();
        }

        function clearSearch() {
            document.getElementById('staffSearch').value = '';
            renderCalendar();
        }

        function showError(message) {
            alert(message);
        }
