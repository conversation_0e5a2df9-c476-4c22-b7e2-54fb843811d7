#!/usr/bin/env python3
"""
Set shift types for staff members
"""

import sqlite3

def set_shift_types():
    """Set shift types for testing"""
    
    conn = sqlite3.connect('vishnorex.db')
    cursor = conn.cursor()
    
    try:
        # Update staff shift types for testing
        cursor.execute('UPDATE staff SET shift_type = ? WHERE id = ?', ('general', 36))  # Mohan
        cursor.execute('UPDATE staff SET shift_type = ? WHERE id = ?', ('general', 37))  # Navanee
        cursor.execute('UPDATE staff SET shift_type = ? WHERE id = ?', ('overtime', 39)) # NN-555
        
        conn.commit()
        
        # Verify updates
        cursor.execute('SELECT id, full_name, shift_type FROM staff')
        staff = cursor.fetchall()
        
        print("Updated staff shift types:")
        for s in staff:
            print(f'Staff {s[0]} ({s[1]}): {s[2] or "Not Set"}')
        
    except Exception as e:
        print(f"Error: {e}")
    finally:
        conn.close()

if __name__ == '__main__':
    set_shift_types()
