#!/usr/bin/env python3
"""
Create database tables for on duty and permission features
"""

import sqlite3

def create_tables():
    """Create on_duty_permissions and permissions tables"""
    
    print("=== Creating On Duty and Permission Tables ===")
    
    conn = sqlite3.connect('vishnorex.db')
    cursor = conn.cursor()
    
    try:
        # Create on_duty_permissions table
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS on_duty_permissions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            staff_id INTEGER NOT NULL,
            school_id INTEGER NOT NULL,
            permission_type TEXT NOT NULL,
            start_datetime TEXT NOT NULL,
            end_datetime TEXT NOT NULL,
            location TEXT,
            reason TEXT,
            status TEXT DEFAULT 'pending',
            applied_at TEXT,
            processed_by INTEGER,
            processed_at TEXT,
            FOREIGN KEY (staff_id) REFERENCES staff (id),
            FOREIGN KEY (school_id) REFERENCES schools (id),
            FOREIGN KEY (processed_by) REFERENCES staff (id)
        )
        ''')
        
        print("✅ Created on_duty_permissions table")
        
        # Create permissions table
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS permissions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            staff_id INTEGER NOT NULL,
            school_id INTEGER NOT NULL,
            permission_type TEXT NOT NULL,
            permission_date TEXT NOT NULL,
            duration TEXT,
            from_time TEXT,
            to_time TEXT,
            reason TEXT,
            status TEXT DEFAULT 'pending',
            applied_at TEXT,
            processed_by INTEGER,
            processed_at TEXT,
            FOREIGN KEY (staff_id) REFERENCES staff (id),
            FOREIGN KEY (school_id) REFERENCES schools (id),
            FOREIGN KEY (processed_by) REFERENCES staff (id)
        )
        ''')
        
        print("✅ Created permissions table")
        
        conn.commit()
        print("\n🎉 Tables created successfully!")
        
    except Exception as e:
        print(f"❌ Error creating tables: {e}")
        conn.rollback()
    finally:
        conn.close()

def check_tables():
    """Check if tables exist and their structure"""
    
    print("\n=== Checking Tables ===")
    
    conn = sqlite3.connect('vishnorex.db')
    cursor = conn.cursor()
    
    try:
        # Check on_duty_permissions table
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='on_duty_permissions'")
        if cursor.fetchone():
            print("✅ on_duty_permissions table exists")
            
            # Check columns
            cursor.execute("PRAGMA table_info(on_duty_permissions)")
            columns = cursor.fetchall()
            print(f"   Columns: {len(columns)}")
            for col in columns:
                print(f"   - {col[1]} ({col[2]})")
        else:
            print("❌ on_duty_permissions table does not exist")
        
        print()
        
        # Check permissions table
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='permissions'")
        if cursor.fetchone():
            print("✅ permissions table exists")
            
            # Check columns
            cursor.execute("PRAGMA table_info(permissions)")
            columns = cursor.fetchall()
            print(f"   Columns: {len(columns)}")
            for col in columns:
                print(f"   - {col[1]} ({col[2]})")
        else:
            print("❌ permissions table does not exist")
        
    except Exception as e:
        print(f"❌ Error checking tables: {e}")
    finally:
        conn.close()

if __name__ == '__main__':
    print("=== On Duty and Permission Tables Setup ===\n")
    
    create_tables()
    check_tables()
    
    print("\n=== Setup Complete ===")
    print("You can now use the On Duty and Permission features in the staff dashboard.")
    print("1. Staff can request on duty for official work, training, etc.")
    print("2. Staff can request permission for late coming, early going, etc.")
    print("3. Admins can approve or reject these requests.")
    print("\nRestart your Flask app to apply the changes.")
