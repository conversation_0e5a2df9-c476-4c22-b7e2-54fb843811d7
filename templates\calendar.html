<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Employee Calendar - {{ school_name }}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .calendar-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem 0;
        }
        
        .employee-card {
            border: 1px solid #e0e0e0;
            border-radius: 12px;
            margin-bottom: 1rem;
            transition: all 0.3s ease;
            background: white;
        }
        
        .employee-card:hover {
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }
        
        .status-badge {
            font-size: 0.85rem;
            padding: 0.4rem 0.8rem;
            border-radius: 20px;
            font-weight: 500;
        }
        
        .status-present { background-color: #d4edda; color: #155724; }
        .status-absent { background-color: #f8d7da; color: #721c24; }
        .status-late { background-color: #fff3cd; color: #856404; }
        .status-leave { background-color: #cce7ff; color: #004085; }
        .status-permission { background-color: #e2e3e5; color: #383d41; }
        .status-onduty { background-color: #d1ecf1; color: #0c5460; }
        
        .shift-info {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 0.75rem;
            margin-bottom: 0.5rem;
        }
        
        .timing-display {
            font-family: 'Courier New', monospace;
            font-weight: bold;
            color: #495057;
        }
        
        .punctuality-issue {
            color: #dc3545;
            font-weight: 500;
        }
        
        .summary-card {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .date-picker {
            max-width: 200px;
        }
        
        .loading-spinner {
            display: none;
        }
        
        .employee-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
            gap: 1rem;
        }
        
        @media (max-width: 768px) {
            .employee-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="calendar-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h1 class="mb-0">
                        <i class="bi bi-calendar3 me-2"></i>
                        Employee Calendar
                    </h1>
                    <p class="mb-0 opacity-75">{{ school_name }} - Comprehensive Attendance Overview</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <div class="d-flex align-items-center justify-content-md-end">
                        <label for="dateSelector" class="form-label text-white me-2 mb-0">Date:</label>
                        <input type="date" id="dateSelector" class="form-control date-picker" 
                               value="{{ today if today else '' }}">
                        <button class="btn btn-light ms-2" onclick="loadCalendarData()">
                            <i class="bi bi-arrow-clockwise"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="container mt-4">
        <!-- Summary Section -->
        <div id="summarySection">
            <div class="summary-card">
                <div class="row text-center">
                    <div class="col-md-2">
                        <h3 id="totalEmployees">0</h3>
                        <small>Total Staff</small>
                    </div>
                    <div class="col-md-2">
                        <h3 id="presentCount">0</h3>
                        <small>Present</small>
                    </div>
                    <div class="col-md-2">
                        <h3 id="absentCount">0</h3>
                        <small>Absent</small>
                    </div>
                    <div class="col-md-2">
                        <h3 id="leaveCount">0</h3>
                        <small>On Leave</small>
                    </div>
                    <div class="col-md-2">
                        <h3 id="lateCount">0</h3>
                        <small>Late Arrivals</small>
                    </div>
                    <div class="col-md-2">
                        <h3 id="earlyCount">0</h3>
                        <small>Early Departures</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Loading Spinner -->
        <div class="text-center loading-spinner" id="loadingSpinner">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-2">Loading calendar data...</p>
        </div>

        <!-- Employee Calendar Grid -->
        <div id="calendarGrid" class="employee-grid">
            <!-- Employee cards will be populated here -->
        </div>

        <!-- No Data Message -->
        <div id="noDataMessage" class="text-center py-5" style="display: none;">
            <i class="bi bi-calendar-x display-1 text-muted"></i>
            <h3 class="text-muted mt-3">No Data Available</h3>
            <p class="text-muted">No employee data found for the selected date.</p>
        </div>
    </div>

    <!-- Employee Detail Modal -->
    <div class="modal fade" id="employeeDetailModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="bi bi-person-circle me-2"></i>
                        <span id="modalEmployeeName">Employee Details</span>
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="modalEmployeeDetails">
                    <!-- Detailed employee information will be loaded here -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Set today's date as default
        document.getElementById('dateSelector').value = new Date().toISOString().split('T')[0];
        
        // Load calendar data on page load
        document.addEventListener('DOMContentLoaded', function() {
            loadCalendarData();
        });
        
        // Date selector change event
        document.getElementById('dateSelector').addEventListener('change', function() {
            loadCalendarData();
        });
        
        function loadCalendarData() {
            const selectedDate = document.getElementById('dateSelector').value;
            if (!selectedDate) return;
            
            showLoading(true);
            
            fetch(`/api/calendar/date/${selectedDate}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        updateSummary(data.summary);
                        renderEmployeeCards(data.employees);
                    } else {
                        showError('Failed to load calendar data: ' + data.error);
                    }
                })
                .catch(error => {
                    showError('Error loading calendar data: ' + error.message);
                })
                .finally(() => {
                    showLoading(false);
                });
        }
        
        function showLoading(show) {
            document.getElementById('loadingSpinner').style.display = show ? 'block' : 'none';
            document.getElementById('calendarGrid').style.display = show ? 'none' : 'block';
        }
        
        function updateSummary(summary) {
            document.getElementById('totalEmployees').textContent = summary.total_employees;
            document.getElementById('presentCount').textContent = summary.present;
            document.getElementById('absentCount').textContent = summary.absent;
            document.getElementById('leaveCount').textContent = summary.on_leave;
            document.getElementById('lateCount').textContent = summary.late_arrivals;
            document.getElementById('earlyCount').textContent = summary.early_departures;
        }
        
        function renderEmployeeCards(employees) {
            const grid = document.getElementById('calendarGrid');
            const noDataMessage = document.getElementById('noDataMessage');
            
            if (employees.length === 0) {
                grid.innerHTML = '';
                noDataMessage.style.display = 'block';
                return;
            }
            
            noDataMessage.style.display = 'none';
            
            grid.innerHTML = employees.map(employee => `
                <div class="employee-card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-start mb-3">
                            <div>
                                <h5 class="card-title mb-1">${employee.employee_name}</h5>
                                <small class="text-muted">ID: ${employee.employee_id}</small>
                            </div>
                            <span class="status-badge ${getStatusClass(employee.summary_status)}">
                                ${employee.summary_status}
                            </span>
                        </div>
                        
                        <div class="shift-info">
                            <div class="row">
                                <div class="col-6">
                                    <strong>Shift:</strong> ${employee.shift_type}
                                </div>
                                <div class="col-6">
                                    <strong>Duration:</strong> ${employee.shift_duration}
                                </div>
                            </div>
                        </div>
                        
                        <div class="row mb-2">
                            <div class="col-6">
                                <small class="text-muted">Check-in:</small><br>
                                <span class="timing-display">${employee.check_in}</span>
                            </div>
                            <div class="col-6">
                                <small class="text-muted">Check-out:</small><br>
                                <span class="timing-display">${employee.check_out}</span>
                            </div>
                        </div>
                        
                        <div class="mb-2">
                            <strong>Status:</strong> ${employee.attendance_status}
                        </div>
                        
                        ${getPunctualityDisplay(employee.punctuality)}
                        
                        <div class="text-end">
                            <button class="btn btn-outline-primary btn-sm" 
                                    onclick="showEmployeeDetails(${employee.employee_id}, '${employee.date}')">
                                <i class="bi bi-eye"></i> View Details
                            </button>
                        </div>
                    </div>
                </div>
            `).join('');
        }
        
        function getStatusClass(status) {
            if (status.includes('Present')) return 'status-present';
            if (status.includes('Absent')) return 'status-absent';
            if (status.includes('Late')) return 'status-late';
            if (status.includes('Leave')) return 'status-leave';
            if (status.includes('Permission')) return 'status-permission';
            if (status.includes('On-Duty')) return 'status-onduty';
            return 'status-present';
        }
        
        function getPunctualityDisplay(punctuality) {
            let html = '';
            if (punctuality.late_arrival) {
                html += `<div class="punctuality-issue"><i class="bi bi-clock"></i> ${punctuality.late_arrival}</div>`;
            }
            if (punctuality.early_departure) {
                html += `<div class="punctuality-issue"><i class="bi bi-clock-history"></i> ${punctuality.early_departure}</div>`;
            }
            return html;
        }
        
        function showEmployeeDetails(employeeId, date) {
            fetch(`/api/calendar/employee/${employeeId}/${date}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        displayEmployeeModal(data.data);
                    } else {
                        showError('Failed to load employee details: ' + data.error);
                    }
                })
                .catch(error => {
                    showError('Error loading employee details: ' + error.message);
                });
        }
        
        function displayEmployeeModal(employee) {
            document.getElementById('modalEmployeeName').textContent = employee.employee_name;
            
            const modalBody = document.getElementById('modalEmployeeDetails');
            modalBody.innerHTML = `
                <div class="row">
                    <div class="col-md-6">
                        <h6>Shift Information</h6>
                        <ul class="list-unstyled">
                            <li><strong>Type:</strong> ${employee.shift_info.shift_type}</li>
                            <li><strong>Duration:</strong> ${employee.shift_info.duration}</li>
                            <li><strong>Schedule:</strong> ${employee.shift_info.start_time} - ${employee.shift_info.end_time}</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>Timing Details</h6>
                        <ul class="list-unstyled">
                            <li><strong>Check-in:</strong> ${employee.timing.check_in}</li>
                            <li><strong>Check-out:</strong> ${employee.timing.check_out}</li>
                        </ul>
                    </div>
                </div>
                
                <div class="row mt-3">
                    <div class="col-md-6">
                        <h6>Attendance Status</h6>
                        <p>${employee.attendance.status_display}</p>
                        ${employee.attendance.leave_type ? `<p><strong>Leave Type:</strong> ${employee.attendance.leave_type}</p>` : ''}
                        ${employee.attendance.permission_duration ? `<p><strong>Permission:</strong> ${employee.attendance.permission_duration}</p>` : ''}
                        ${employee.attendance.on_duty_details ? `<p><strong>On-Duty:</strong> ${employee.attendance.on_duty_details}</p>` : ''}
                    </div>
                    <div class="col-md-6">
                        <h6>Punctuality</h6>
                        ${employee.punctuality.late_arrival ? `<p class="text-warning">${employee.punctuality.late_arrival}</p>` : ''}
                        ${employee.punctuality.early_departure ? `<p class="text-warning">${employee.punctuality.early_departure}</p>` : ''}
                        ${!employee.punctuality.has_issues ? '<p class="text-success">No punctuality issues</p>' : ''}
                    </div>
                </div>
                
                <div class="mt-3">
                    <h6>Summary</h6>
                    <span class="status-badge ${getStatusClass(employee.summary_status)}">${employee.summary_status}</span>
                </div>
            `;
            
            new bootstrap.Modal(document.getElementById('employeeDetailModal')).show();
        }
        
        function showError(message) {
            alert(message); // Simple error display - can be enhanced with toast notifications
        }
    </script>
</body>
</html>
