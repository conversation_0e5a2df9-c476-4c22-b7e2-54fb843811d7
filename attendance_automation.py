#!/usr/bin/env python3
"""
Automated Attendance Tracking System
Based on verification times for General and Overtime shifts
"""

from datetime import datetime, time, timedelta
import sqlite3
from typing import Dict, List, Optional, Tuple

class AttendanceAutomation:
    """
    Automates attendance tracking based on shift schedules and verification times
    """
    
    # Shift Schedules
    GENERAL_SHIFT = {
        'start_time': time(9, 20),  # 9:20 AM
        'end_time': time(16, 30),   # 4:30 PM
        'name': 'General Shift'
    }
    
    OVERTIME_SHIFT = {
        'start_time': time(9, 20),  # 9:20 AM
        'end_time': time(17, 30),   # 5:30 PM
        'name': 'Overtime Shift'
    }
    
    # Grace period for arrival
    GRACE_PERIOD_END = time(9, 30)  # 9:30 AM
    
    def __init__(self, db_path='vishnorex.db'):
        self.db_path = db_path
    
    def get_shift_schedule(self, shift_type: str) -> Dict:
        """Get shift schedule based on shift type"""
        if not shift_type:
            return self.GENERAL_SHIFT

        shift_type_lower = shift_type.lower().strip()
        if shift_type_lower in ['overtime', 'over', 'overtime shift']:
            return self.OVERTIME_SHIFT
        else:
            # Default to general shift for 'general', 'general shift', or any other value
            return self.GENERAL_SHIFT
    
    def calculate_time_difference(self, actual_time: time, expected_time: time) -> Tuple[int, str]:
        """
        Calculate time difference in minutes and return formatted string
        Returns: (minutes_difference, formatted_string)
        """
        actual_dt = datetime.combine(datetime.today(), actual_time)
        expected_dt = datetime.combine(datetime.today(), expected_time)
        
        diff = actual_dt - expected_dt
        minutes = int(diff.total_seconds() / 60)
        
        if minutes > 0:
            return minutes, f"{minutes} minutes"
        elif minutes < 0:
            return abs(minutes), f"{abs(minutes)} minutes"
        else:
            return 0, "0 minutes"
    
    def determine_morning_status(self, verification_time: time, shift_type: str) -> Dict:
        """
        Determine morning attendance status based on verification time
        """
        shift_schedule = self.get_shift_schedule(shift_type)
        
        # Rule 1: Present (within grace period)
        if shift_schedule['start_time'] <= verification_time <= self.GRACE_PERIOD_END:
            return {
                'status': 'Present',
                'message': 'Present',
                'verification_time': verification_time.strftime('%I:%M %p'),
                'details': None
            }
        
        # Rule 2: Late Arrival
        elif verification_time > self.GRACE_PERIOD_END:
            delay_minutes, delay_text = self.calculate_time_difference(
                verification_time, self.GRACE_PERIOD_END
            )
            
            return {
                'status': 'Late',
                'message': f'Status: Late. Verified at {verification_time.strftime("%I:%M %p")} (Late by {delay_text}).',
                'verification_time': verification_time.strftime('%I:%M %p'),
                'details': f'Late by {delay_text}'
            }
        
        # Before shift start time (very early)
        else:
            return {
                'status': 'Present',
                'message': f'Present (Early arrival at {verification_time.strftime("%I:%M %p")})',
                'verification_time': verification_time.strftime('%I:%M %p'),
                'details': 'Early arrival'
            }
    
    def determine_evening_status(self, verification_time: time, shift_type: str, morning_status: str) -> Dict:
        """
        Determine evening attendance status based on verification time
        """
        shift_schedule = self.get_shift_schedule(shift_type)
        
        # Rule 3: Early Departure (Left Soon)
        if verification_time < shift_schedule['end_time']:
            early_minutes, early_text = self.calculate_time_difference(
                shift_schedule['end_time'], verification_time
            )
            
            # Combine with morning status if applicable
            if morning_status == 'Late':
                status_prefix = 'Late & Left Soon'
                message = f'Status: {status_prefix}. Verified out at {verification_time.strftime("%I:%M %p")} (Left {early_text} early).'
            else:
                status_prefix = 'Left Soon'
                message = f'Status: Left Soon. Verified out at {verification_time.strftime("%I:%M %p")} (Left {early_text} early).'

            return {
                'status': status_prefix,
                'message': message,
                'verification_time': verification_time.strftime('%I:%M %p'),
                'details': f'Left {early_text} early'
            }
        
        # On time or overtime departure
        else:
            if verification_time > shift_schedule['end_time']:
                overtime_minutes, overtime_text = self.calculate_time_difference(
                    verification_time, shift_schedule['end_time']
                )
                details = f'Overtime: {overtime_text}'
            else:
                details = 'On time departure'
            
            # Maintain morning status if it was late
            if morning_status == 'Late':
                return {
                    'status': 'Late',
                    'message': f'Status: Late. Verified out at {verification_time.strftime("%I:%M %p")}.',
                    'verification_time': verification_time.strftime('%I:%M %p'),
                    'details': details
                }
            else:
                return {
                    'status': 'Present',
                    'message': f'Present. Verified out at {verification_time.strftime("%I:%M %p")}.',
                    'verification_time': verification_time.strftime('%I:%M %p'),
                    'details': details
                }
    
    def process_daily_attendance(self, staff_id: int, date: str, verifications: List[Dict]) -> Dict:
        """
        Process all verifications for a staff member on a specific date
        verifications: List of {'time': time_obj, 'type': 'check-in'/'check-out'}
        """
        
        # Get staff shift type
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        try:
            cursor.execute('SELECT shift_type FROM staff WHERE id = ?', (staff_id,))
            staff_record = cursor.fetchone()
            shift_type = staff_record['shift_type'] if staff_record else 'general'
        finally:
            conn.close()
        
        # Sort verifications by time
        verifications.sort(key=lambda x: x['time'])
        
        # Find first check-in and last check-out
        check_ins = [v for v in verifications if v['type'] == 'check-in']
        check_outs = [v for v in verifications if v['type'] == 'check-out']
        
        morning_status = None
        evening_status = None
        final_status = 'Present'
        final_message = 'Present'
        morning_message = None
        time_in = None
        time_out = None

        # Process morning verification (first check-in)
        if check_ins:
            first_check_in = check_ins[0]
            time_in = first_check_in['time']
            morning_result = self.determine_morning_status(first_check_in['time'], shift_type)
            morning_status = morning_result['status']
            morning_message = morning_result['message']
            final_status = morning_result['status']
            final_message = morning_result['message']

        # Process evening verification (last check-out)
        if check_outs:
            last_check_out = check_outs[-1]
            time_out = last_check_out['time']
            evening_result = self.determine_evening_status(
                last_check_out['time'], shift_type, morning_status or 'Present'
            )
            final_status = evening_result['status']

            # For late arrivals, preserve the morning timing details
            if morning_status == 'Late' and evening_result['status'] == 'Late':
                final_message = morning_message  # Keep the original late message with timing
            else:
                final_message = evening_result['message']
        
        return {
            'staff_id': staff_id,
            'date': date,
            'shift_type': shift_type,
            'time_in': time_in.strftime('%H:%M:%S') if time_in else None,
            'time_out': time_out.strftime('%H:%M:%S') if time_out else None,
            'status': final_status,
            'message': final_message,
            'morning_status': morning_status,
            'evening_status': evening_status
        }
    
    def map_status_for_database(self, automated_status: str) -> str:
        """
        Map automated status to database-compatible status
        Database only allows: 'present', 'absent', 'late', 'leave'
        """
        status_mapping = {
            'Present': 'present',
            'Late': 'late',
            'Left Soon': 'absent',  # Early departure treated as absent
            'Late & Left Soon': 'late'  # Late arrival takes precedence
        }

        return status_mapping.get(automated_status, 'present')

    def update_attendance_record(self, attendance_data: Dict) -> bool:
        """
        Update attendance record in database with calculated status
        """
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        try:
            # Map status to database-compatible value
            db_status = self.map_status_for_database(attendance_data['status'])

            # Check if attendance record exists
            cursor.execute('''
                SELECT id FROM attendance
                WHERE staff_id = ? AND date = ?
            ''', (attendance_data['staff_id'], attendance_data['date']))

            existing = cursor.fetchone()

            if existing:
                # Update existing record
                cursor.execute('''
                    UPDATE attendance
                    SET time_in = ?, time_out = ?, status = ?, notes = ?
                    WHERE staff_id = ? AND date = ?
                ''', (
                    attendance_data['time_in'],
                    attendance_data['time_out'],
                    db_status,
                    attendance_data['message'],
                    attendance_data['staff_id'],
                    attendance_data['date']
                ))
            else:
                # Get school_id for the staff
                cursor.execute('SELECT school_id FROM staff WHERE id = ?', (attendance_data['staff_id'],))
                staff_record = cursor.fetchone()
                school_id = staff_record[0] if staff_record else 1

                # Create new record
                cursor.execute('''
                    INSERT INTO attendance
                    (staff_id, school_id, date, time_in, time_out, status, notes)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (
                    attendance_data['staff_id'],
                    school_id,
                    attendance_data['date'],
                    attendance_data['time_in'],
                    attendance_data['time_out'],
                    db_status,
                    attendance_data['message']
                ))

            conn.commit()
            return True

        except Exception as e:
            print(f"Error updating attendance: {e}")
            conn.rollback()
            return False
        finally:
            conn.close()
    
    def process_biometric_verifications(self, date: str = None) -> List[Dict]:
        """
        Process all biometric verifications for a specific date
        If date is None, process today's verifications
        """
        if date is None:
            date = datetime.now().strftime('%Y-%m-%d')
        
        # This would typically get data from biometric device
        # For now, we'll simulate with database data
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        results = []
        
        try:
            # Get all staff
            cursor.execute('SELECT id, staff_id, full_name, shift_type FROM staff')
            staff_members = cursor.fetchall()
            
            for staff in staff_members:
                # Simulate getting verifications from biometric device
                # In real implementation, this would call biometric device API
                verifications = self.get_staff_verifications(staff['id'], date)
                
                if verifications:
                    attendance_data = self.process_daily_attendance(
                        staff['id'], date, verifications
                    )
                    
                    # Update database
                    success = self.update_attendance_record(attendance_data)
                    
                    results.append({
                        'staff_name': staff['full_name'],
                        'staff_id': staff['staff_id'],
                        'attendance_data': attendance_data,
                        'updated': success
                    })
        
        finally:
            conn.close()
        
        return results
    
    def get_staff_verifications(self, staff_id: int, date: str) -> List[Dict]:
        """
        Get staff verifications for a specific date
        This would typically interface with biometric device
        For now, simulate with sample data
        """
        # This is a placeholder - in real implementation, 
        # this would get data from biometric device or verification logs
        
        # Sample verification data for testing
        sample_verifications = [
            {'time': time(9, 25), 'type': 'check-in'},   # On time
            {'time': time(16, 30), 'type': 'check-out'}  # On time
        ]
        
        return sample_verifications
