#!/usr/bin/env python3
"""
Fix attendance status constraint to support new automated statuses
"""

import sqlite3

def check_and_fix_status_constraint():
    """Check and fix the status constraint in attendance table"""
    
    conn = sqlite3.connect('vishnorex.db')
    cursor = conn.cursor()
    
    try:
        # Check current table definition
        cursor.execute('SELECT sql FROM sqlite_master WHERE type="table" AND name="attendance"')
        table_def = cursor.fetchone()
        
        if table_def:
            print("Current attendance table definition:")
            print(table_def[0])
            print()
        
        # Check if we can insert the new status values
        test_statuses = ['present', 'absent', 'late', 'leave', 'left soon', 'late & left soon']
        
        print("Testing status values:")
        for status in test_statuses:
            try:
                # Try to insert a test record
                cursor.execute('''
                    INSERT INTO attendance (staff_id, school_id, date, status, notes)
                    VALUES (?, ?, ?, ?, ?)
                ''', (36, 4, '2025-07-17', status.lower(), 'Test record'))
                
                # If successful, delete the test record
                cursor.execute('DELETE FROM attendance WHERE notes = "Test record"')
                print(f"✅ '{status}' - Allowed")
                
            except sqlite3.IntegrityError as e:
                print(f"❌ '{status}' - Blocked: {e}")
        
        # The issue is likely that we need to map our statuses to the allowed ones
        print("\nSolution: Map automated statuses to allowed database values")
        print("- 'Left Soon' -> 'absent' (with detailed notes)")
        print("- 'Late & Left Soon' -> 'late' (with detailed notes)")
        print("- 'Late' -> 'late'")
        print("- 'Present' -> 'present'")
        
    except Exception as e:
        print(f"Error: {e}")
    finally:
        conn.close()

def update_attendance_automation_mapping():
    """Update the attendance automation to use proper status mapping"""
    
    print("\nStatus Mapping for Database Compatibility:")
    print("=" * 50)
    
    status_mapping = {
        'Present': 'present',
        'Late': 'late', 
        'Left Soon': 'absent',  # Use absent with detailed notes
        'Late & Left Soon': 'late'  # Use late with detailed notes
    }
    
    for automated_status, db_status in status_mapping.items():
        print(f"{automated_status:15} -> {db_status}")
    
    print("\nThe detailed timing information will be stored in the 'notes' field.")

if __name__ == '__main__':
    print("=== Attendance Status Constraint Analysis ===\n")
    
    check_and_fix_status_constraint()
    update_attendance_automation_mapping()
    
    print("\n" + "=" * 60)
    print("RECOMMENDATION:")
    print("Update the attendance_automation.py to map statuses correctly:")
    print("- Keep detailed messages in 'notes' field")
    print("- Use standard status values for 'status' field")
    print("- This maintains compatibility while preserving detail")
