#!/usr/bin/env python3
"""
Create test staff data for calendar testing
"""

import sqlite3
from datetime import datetime
import hashlib

def create_test_staff():
    """Create test staff members for calendar testing"""
    
    print("=== Creating Test Staff Data ===\n")
    
    conn = sqlite3.connect('vishnorex.db')
    cursor = conn.cursor()
    
    try:
        # Test staff data
        test_staff = [
            {
                'staff_id': '100',
                'full_name': '<PERSON><PERSON><PERSON>',
                'email': '<EMAIL>',
                'phone': '9876543210',
                'department': 'Computer Science',
                'position': 'Assistant Professor',
                'shift_type': 'general',
                'password': '100'
            },
            {
                'staff_id': '101',
                'full_name': '<PERSON><PERSON>',
                'email': 'r<PERSON><PERSON>@bharathiyar.edu',
                'phone': '9876543211',
                'department': 'Mathematics',
                'position': 'Associate Professor',
                'shift_type': 'general',
                'password': '101'
            },
            {
                'staff_id': '102',
                'full_name': '<PERSON><PERSON>',
                'email': '<EMAIL>',
                'phone': '9876543212',
                'department': 'Physics',
                'position': 'Professor',
                'shift_type': 'overtime',
                'password': '102'
            },
            {
                'staff_id': '103',
                'full_name': 'Amit Singh',
                'email': '<EMAIL>',
                'phone': '9876543213',
                'department': 'Chemistry',
                'position': 'Lecturer',
                'shift_type': 'general',
                'password': '103'
            },
            {
                'staff_id': '104',
                'full_name': 'Sunita Reddy',
                'email': '<EMAIL>',
                'phone': '9876543214',
                'department': 'Biology',
                'position': 'Assistant Professor',
                'shift_type': 'overtime',
                'password': '104'
            }
        ]
        
        school_id = 4  # Bharathiyar school
        
        for staff in test_staff:
            # Check if staff already exists
            cursor.execute('SELECT id FROM staff WHERE staff_id = ? AND school_id = ?', 
                         (staff['staff_id'], school_id))
            existing = cursor.fetchone()
            
            if existing:
                print(f"✅ Staff {staff['full_name']} already exists (ID: {staff['staff_id']})")
                continue
            
            # Hash password
            password_hash = hashlib.sha256(staff['password'].encode()).hexdigest()
            
            # Insert staff member
            cursor.execute('''
                INSERT INTO staff
                (school_id, staff_id, full_name, email, phone, department, position,
                 shift_type, password_hash, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                school_id, staff['staff_id'], staff['full_name'], staff['email'],
                staff['phone'], staff['department'], staff['position'],
                staff['shift_type'], password_hash, datetime.now()
            ))
            
            print(f"✅ Created staff: {staff['full_name']} (ID: {staff['staff_id']}, Shift: {staff['shift_type']})")
        
        conn.commit()
        
        # Verify creation
        cursor.execute('SELECT COUNT(*) FROM staff WHERE school_id = ?', (school_id,))
        total_staff = cursor.fetchone()[0]
        
        print(f"\n📊 Total staff in Bharathiyar school: {total_staff}")
        
        # Show all staff
        cursor.execute('''
            SELECT staff_id, full_name, shift_type FROM staff 
            WHERE school_id = ? ORDER BY staff_id
        ''', (school_id,))
        
        all_staff = cursor.fetchall()
        print("\n📋 All Staff Members:")
        for staff in all_staff:
            print(f"   {staff[0]} - {staff[1]} ({staff[2]} shift)")
        
        return True
        
    except Exception as e:
        print(f"❌ Error creating test staff: {e}")
        conn.rollback()
        return False
    finally:
        conn.close()

def create_test_attendance():
    """Create test attendance data for today"""
    
    print("\n=== Creating Test Attendance Data ===\n")
    
    conn = sqlite3.connect('vishnorex.db')
    cursor = conn.cursor()
    
    try:
        today = datetime.now().strftime('%Y-%m-%d')
        school_id = 4
        
        # Clear existing attendance for today
        cursor.execute('DELETE FROM attendance WHERE date = ? AND school_id = ?', (today, school_id))
        
        # Get staff members
        cursor.execute('SELECT id, staff_id, full_name, shift_type FROM staff WHERE school_id = ?', (school_id,))
        staff_members = cursor.fetchall()
        
        if not staff_members:
            print("❌ No staff members found")
            return False
        
        # Create various attendance scenarios
        attendance_scenarios = [
            {
                'time_in': '09:25:00',
                'time_out': '16:30:00',
                'status': 'present',
                'notes': 'Perfect attendance'
            },
            {
                'time_in': '09:50:00',
                'time_out': '16:30:00',
                'status': 'late',
                'notes': 'Late arrival by 20 minutes'
            },
            {
                'time_in': '09:25:00',
                'time_out': '16:15:00',
                'status': 'present',
                'notes': 'Early departure by 15 minutes'
            },
            {
                'time_in': '09:45:00',
                'time_out': '16:15:00',
                'status': 'late',
                'notes': 'Late arrival and early departure'
            },
            {
                'time_in': '09:20:00',
                'time_out': '17:45:00',
                'status': 'present',
                'notes': 'Overtime work'
            }
        ]
        
        for i, staff in enumerate(staff_members[:len(attendance_scenarios)]):
            scenario = attendance_scenarios[i]
            
            cursor.execute('''
                INSERT INTO attendance 
                (staff_id, school_id, date, time_in, time_out, status, notes)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (
                staff[0], school_id, today,
                scenario['time_in'], scenario['time_out'],
                scenario['status'], scenario['notes']
            ))
            
            print(f"✅ Created attendance: {staff[2]} - {scenario['notes']}")
        
        conn.commit()
        print(f"\n✅ Test attendance data created for {len(attendance_scenarios)} staff members")
        return True
        
    except Exception as e:
        print(f"❌ Error creating test attendance: {e}")
        conn.rollback()
        return False
    finally:
        conn.close()

if __name__ == '__main__':
    print("🧪 CREATING TEST DATA FOR CALENDAR MODEL")
    print("=" * 60)
    
    # Create test staff
    if create_test_staff():
        # Create test attendance
        create_test_attendance()
    
    print("\n" + "=" * 60)
    print("✅ TEST DATA CREATION COMPLETE")
    print("\nYou can now:")
    print("1. Run the calendar model tests: python test_calendar_model.py")
    print("2. Access the calendar view in the web interface")
    print("3. Test the API endpoints")
    print("\nLogin credentials for test staff:")
    print("- Username: 100, Password: 100 (Navanee Kumar)")
    print("- Username: 101, Password: 101 (Rajesh Sharma)")
    print("- Username: 102, Password: 102 (Priya Patel)")
    print("- Username: 103, Password: 103 (Amit Singh)")
    print("- Username: 104, Password: 104 (Sunita Reddy)")
