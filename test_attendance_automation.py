#!/usr/bin/env python3
"""
Test Automated Attendance Tracking System
Demonstrates the shift-based attendance automation
"""

from datetime import datetime, time
from attendance_automation import AttendanceAutomation
import sqlite3

def test_attendance_scenarios():
    """Test various attendance scenarios"""
    
    print("=== Automated Attendance Tracking System Test ===\n")
    
    automation = AttendanceAutomation()
    
    # Test scenarios as per requirements
    test_cases = [
        {
            'name': 'General Shift - Perfect Attendance',
            'shift_type': 'general',
            'verifications': [
                {'time': time(9, 25), 'type': 'check-in'},   # Within grace period
                {'time': time(16, 30), 'type': 'check-out'}  # Exact end time
            ],
            'expected': 'Present'
        },
        {
            'name': 'General Shift - Late Arrival (20 minutes)',
            'shift_type': 'general',
            'verifications': [
                {'time': time(9, 50), 'type': 'check-in'},   # 20 minutes late
                {'time': time(16, 30), 'type': 'check-out'}  # On time departure
            ],
            'expected': 'Status: Late. Verified at 9:50 AM (Late by 20 minutes).'
        },
        {
            'name': 'Overtime Shift - Left Early (30 minutes)',
            'shift_type': 'overtime',
            'verifications': [
                {'time': time(9, 22), 'type': 'check-in'},   # On time
                {'time': time(17, 0), 'type': 'check-out'}   # 30 minutes early
            ],
            'expected': 'Status: Left Soon. Verified out at 5:00 PM (Left 30 minutes early).'
        },
        {
            'name': 'General Shift - Late & Left Early',
            'shift_type': 'general',
            'verifications': [
                {'time': time(9, 45), 'type': 'check-in'},   # 15 minutes late
                {'time': time(16, 15), 'type': 'check-out'}  # 15 minutes early
            ],
            'expected': 'Late & Left Soon'
        },
        {
            'name': 'Overtime Shift - Overtime Work',
            'shift_type': 'overtime',
            'verifications': [
                {'time': time(9, 20), 'type': 'check-in'},   # Exact start time
                {'time': time(18, 0), 'type': 'check-out'}   # 30 minutes overtime
            ],
            'expected': 'Present'
        },
        {
            'name': 'General Shift - Very Early Arrival',
            'shift_type': 'general',
            'verifications': [
                {'time': time(8, 30), 'type': 'check-in'},   # 50 minutes early
                {'time': time(16, 30), 'type': 'check-out'}  # On time
            ],
            'expected': 'Present'
        }
    ]
    
    print("Testing Attendance Scenarios:")
    print("=" * 80)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{i}. {test_case['name']}")
        print(f"   Shift Type: {test_case['shift_type'].title()}")
        
        # Show verifications
        for verification in test_case['verifications']:
            print(f"   {verification['type'].title()}: {verification['time'].strftime('%I:%M %p')}")
        
        # Process attendance (use actual staff ID based on shift type)
        if test_case['shift_type'] == 'overtime':
            staff_id = 39  # NN-555 (overtime shift)
        else:
            staff_id = 36  # Mohan (general shift)

        result = automation.process_daily_attendance(
            staff_id=staff_id,
            date=datetime.now().strftime('%Y-%m-%d'),
            verifications=test_case['verifications']
        )
        
        print(f"   Result: {result['message']}")
        print(f"   Status: {result['status']}")
        
        # Verify against expected (simplified check)
        if test_case['expected'] in result['message'] or test_case['expected'] == result['status']:
            print("   ✅ PASSED")
        else:
            print("   ❌ FAILED")
            print(f"   Expected: {test_case['expected']}")
        
        print("-" * 80)

def demonstrate_shift_rules():
    """Demonstrate the shift rules and calculations"""
    
    print("\n=== Shift Rules Demonstration ===\n")
    
    automation = AttendanceAutomation()
    
    print("SHIFT SCHEDULES:")
    print("General Shift: 9:20 AM - 4:30 PM")
    print("Overtime Shift: 9:20 AM - 5:30 PM")
    print("Grace Period: 9:20 AM - 9:30 AM")
    print()
    
    print("ATTENDANCE RULES:")
    print("1. Present: Arrival between 9:20 AM - 9:30 AM")
    print("2. Late: Arrival after 9:30 AM")
    print("3. Left Soon: Departure before shift end time")
    print()
    
    # Test morning status determination
    print("MORNING STATUS EXAMPLES:")
    morning_times = [
        (time(9, 20), "Exact start time"),
        (time(9, 25), "Within grace period"),
        (time(9, 30), "End of grace period"),
        (time(9, 35), "5 minutes late"),
        (time(9, 50), "20 minutes late"),
        (time(10, 15), "55 minutes late")
    ]
    
    for test_time, description in morning_times:
        result = automation.determine_morning_status(test_time, 'general')
        print(f"   {test_time.strftime('%I:%M %p')} ({description}): {result['message']}")
    
    print()
    
    # Test evening status determination
    print("EVENING STATUS EXAMPLES (General Shift):")
    evening_times = [
        (time(16, 15), "15 minutes early"),
        (time(16, 30), "Exact end time"),
        (time(16, 45), "15 minutes overtime"),
        (time(17, 0), "30 minutes overtime")
    ]
    
    for test_time, description in evening_times:
        result = automation.determine_evening_status(test_time, 'general', 'Present')
        print(f"   {test_time.strftime('%I:%M %p')} ({description}): {result['message']}")

def create_sample_attendance_data():
    """Create sample attendance data in database for testing"""
    
    print("\n=== Creating Sample Attendance Data ===\n")
    
    automation = AttendanceAutomation()
    
    # Sample data for different scenarios
    sample_data = [
        {
            'staff_id': 36,  # Mohan (general shift)
            'date': '2025-07-17',
            'verifications': [
                {'time': time(9, 25), 'type': 'check-in'},
                {'time': time(16, 30), 'type': 'check-out'}
            ]
        },
        {
            'staff_id': 37,  # Navanee (general shift)
            'date': '2025-07-17',
            'verifications': [
                {'time': time(9, 50), 'type': 'check-in'},
                {'time': time(16, 30), 'type': 'check-out'}
            ]
        },
        {
            'staff_id': 39,  # NN-555 (overtime shift)
            'date': '2025-07-17',
            'verifications': [
                {'time': time(9, 22), 'type': 'check-in'},
                {'time': time(17, 0), 'type': 'check-out'}
            ]
        }
    ]
    
    for data in sample_data:
        result = automation.process_daily_attendance(
            data['staff_id'], data['date'], data['verifications']
        )
        
        success = automation.update_attendance_record(result)
        
        print(f"Staff ID {data['staff_id']}: {result['message']} - {'✅ Saved' if success else '❌ Failed'}")

def show_calendar_entries():
    """Show how calendar entries would look"""
    
    print("\n=== Calendar Entry Examples ===\n")
    
    examples = [
        {
            'scenario': 'General Shift employee - arrives 9:25 AM, leaves 4:30 PM',
            'entry': 'Present'
        },
        {
            'scenario': 'General Shift employee - arrives 9:50 AM, leaves 4:30 PM',
            'entry': 'Status: Late. Verified at 9:50 AM (Late by 20 minutes).'
        },
        {
            'scenario': 'Overtime Shift employee - arrives 9:22 AM, leaves 5:00 PM',
            'entry': 'Status: Left Soon. Verified out at 5:00 PM (Left 30 minutes early).'
        },
        {
            'scenario': 'General Shift employee - arrives 9:45 AM, leaves 4:15 PM',
            'entry': 'Status: Late & Left Soon. Multiple violations detected.'
        }
    ]
    
    for example in examples:
        print(f"Scenario: {example['scenario']}")
        print(f"Calendar Entry: \"{example['entry']}\"")
        print()

if __name__ == '__main__':
    print("🕒 AUTOMATED ATTENDANCE TRACKING SYSTEM")
    print("Based on Shift Schedules and Verification Times")
    print("=" * 60)
    
    # Run all tests
    demonstrate_shift_rules()
    test_attendance_scenarios()
    show_calendar_entries()
    
    # Optionally create sample data
    try:
        create_sample_attendance_data()
    except Exception as e:
        print(f"Note: Could not create sample data - {e}")
    
    print("\n" + "=" * 60)
    print("✅ AUTOMATED ATTENDANCE SYSTEM READY")
    print("Features:")
    print("- Automatic status calculation based on verification times")
    print("- Support for General and Overtime shifts")
    print("- Detailed timing and duration tracking")
    print("- Calendar integration with descriptive entries")
    print("- Real-time processing of biometric verifications")
    print("\nTo use in production:")
    print("1. Configure staff shift types in database")
    print("2. Sync biometric device data")
    print("3. Run automated processing")
    print("4. View results in calendar and reports")
